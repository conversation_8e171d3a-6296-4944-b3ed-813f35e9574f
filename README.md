# QuizFlow

QuizFlow is a standardized, flexible, and interactive quiz ecosystem for creating, sharing, and taking quizzes. It defines and implements a comprehensive JSON-based file format for quizzes, named QuizFlow JSON (QFJSON).

## Features

- **Standardized Format**: Create quizzes using a well-documented, extensible, and universally understandable JSON format.
- **Rich Interactivity**: Support for multiple question types, media integration, and dynamic content.
- **User Authentication**: Secure user accounts with email/password and OAuth providers.
- **Quiz Management**: Create, edit, and publish quizzes with various question types.
- **Responsive Design**: Works on desktop and mobile devices.

## Tech Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Next.js API Routes
- **Database**: MongoDB with Prisma ORM
- **Authentication**: NextAuth.js

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Docker and Docker Compose

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/quizflow.git
   cd quizflow
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start MongoDB with Docker:
   ```bash
   docker-compose up -d
   ```

4. Initialize MongoDB replica set:
   ```bash
   docker exec quizflow-mongodb mongosh --eval "rs.initiate({_id: 'rs0', members: [{_id: 0, host: 'localhost:27017'}]})"
   ```

5. Set up the enhanced database structure and content:
   ```bash
   # Full setup with enhanced structure and generated content
   npm run setup:full

   # OR step by step:
   npm run db:push              # Initialize database
   npm run seed:enhanced        # Create categories, difficulty levels, learning paths
   npm run seed                 # Seed original 6 quizzes
   npm run generate:content     # Generate additional CVE-based and real-world scenarios
   ```

6. Run the development server:
   ```bash
   npm run dev
   ```

7. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Enhanced Setup Commands

- **`npm run setup:full`** - Complete setup with all content (development)
- **`npm run setup:production`** - Production setup with enhanced structure and generated content
- **`npm run seed:enhanced`** - Set up categories, difficulty levels, and learning paths
- **`npm run generate:content`** - Generate CVE-based and real-world scenario quizzes
- **`npm run db:reset`** - Reset database (use with caution)

### Scalable Quiz Architecture

QuizFlow now supports a comprehensive, scalable architecture designed for 1000+ cybersecurity questions:

#### **Category-Based Organization (10 Major Categories):**
1. **Web Application Security** (200+ questions) - OWASP Top 10, SQL injection, XSS, API security
2. **Cryptography & Encryption** (150+ questions) - Symmetric/asymmetric encryption, hash functions, PKI
3. **Network Security** (150+ questions) - Network scanning, protocol analysis, wireless security
4. **Mobile Security** (100+ questions) - Android/iOS security, mobile app testing
5. **Cloud Security** (100+ questions) - AWS/Azure/GCP security, container security
6. **Social Engineering** (75+ questions) - Phishing, pretexting, OSINT, physical security
7. **Malware Analysis** (75+ questions) - Static/dynamic analysis, reverse engineering
8. **Incident Response** (50+ questions) - Digital forensics, threat hunting, incident handling
9. **Compliance & Governance** (50+ questions) - ISO 27001, NIST, PCI DSS, GDPR
10. **Emerging Threats** (50+ questions) - AI/ML security, IoT, blockchain, supply chain attacks

#### **Question Types & Interactive Challenges:**
- **Real-World Scenarios**: Based on actual CVE vulnerabilities and security incidents
- **Tool-Specific Challenges**: Hands-on questions for tools like Nmap, Burp Suite, Metasploit
- **Hash Cracking Scenarios**: Practical cryptography challenges
- **Code Vulnerability Identification**: Secure coding assessments
- **Network Traffic Analysis**: Packet analysis and forensics
- **Multi-Step Attack Chains**: Complex penetration testing scenarios

#### **Difficulty Progression:**
- **Beginner (300 questions)**: Basic concepts, fundamental security principles
- **Intermediate (500 questions)**: Real-world scenarios, practical tool usage
- **Advanced (200 questions)**: Complex multi-step attacks, expert-level knowledge

#### **Enhanced Features:**
- **CVE-Based Questions**: Direct integration with real vulnerability databases
- **Learning Paths**: Structured progression through cybersecurity domains
- **Skill Tracking**: Individual skill level monitoring and progression
- **Advanced Analytics**: Detailed performance metrics and learning insights
- **Gamification**: Points, badges, streaks, and achievement systems

### Admin Access

#### Default Admin Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: admin

#### Admin Setup
To create or reset the admin user:
```bash
npm run setup-admin
```

#### Admin Features
- **Admin Dashboard**: Access via `/dashboard/admin` (visible only to admin users)
- **User Management**: View and manage all users
- **Quiz Management**: Manage all quizzes across the platform
- **System Analytics**: View comprehensive platform statistics
- **Admin Navigation**: Special admin menu item in the dashboard navbar

## Enhanced API Endpoints

### Advanced Search & Filtering
- **`GET /api/quizzes/search`** - Advanced quiz search with faceted filtering
  - Query parameters: `q`, `category`, `difficulty`, `tools`, `realWorld`, `cve`, `sortBy`
  - Returns: Paginated results with facets for dynamic filtering UI
  - Supports: Full-text search, category filtering, difficulty levels, tool-specific filtering

### Learning Paths
- **`GET /api/learning-paths`** - Retrieve available learning paths
- **`POST /api/learning-paths/enroll`** - Enroll in a learning path
- **`GET /api/learning-paths/progress`** - Track learning path progress

### Enhanced Analytics
- **`GET /api/analytics/skills`** - Individual skill tracking and progression
- **`GET /api/analytics/performance`** - Detailed performance metrics
- **`GET /api/analytics/leaderboard`** - Gamification leaderboards

## QuizFlow JSON (QFJSON) Format

QuizFlow uses a standardized JSON format for quizzes with enhanced features:

### Core Features:
- Multiple question types (multiple choice, true/false, short answer, matching, fill-in-the-blank, essay)
- Rich media integration (images, audio, video)
- Multilingual content support
- Dynamic question selection from pools
- Detailed scoring rules and feedback

### Enhanced Features:
- **CVE Integration**: Direct reference to CVE vulnerabilities
- **Tool Requirements**: Specify required tools for hands-on questions
- **Real-World Scenarios**: Flag questions based on actual security incidents
- **Skill Mapping**: Map questions to specific cybersecurity skills
- **Difficulty Granularity**: Fine-grained difficulty assessment
- **Time Estimation**: Per-question time estimates for better planning

## Project Structure

```
quizflow/
├── prisma/              # Prisma schema and migrations
├── public/              # Static assets
├── src/
│   ├── app/             # Next.js app router
│   │   ├── api/         # API routes
│   │   ├── auth/        # Authentication pages
│   │   ├── dashboard/   # Dashboard pages
│   │   └── ...
│   ├── components/      # React components
│   │   ├── auth/        # Authentication components
│   │   ├── dashboard/   # Dashboard components
│   │   ├── quiz/        # Quiz components
│   │   └── ui/          # UI components
│   ├── lib/             # Utility functions
│   └── types/           # TypeScript type definitions
└── ...
```
