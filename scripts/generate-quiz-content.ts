#!/usr/bin/env tsx

/**
 * Quiz Content Generation Script
 * 
 * This script generates large-scale quiz content based on:
 * - Real CVE database
 * - Security research papers
 * - Bug bounty reports
 * - Industry best practices
 */

import { PrismaClient } from '@/generated/prisma';
import { QuizFlowJSON } from '@/types/qfjson';

const prisma = new PrismaClient();

// Real-world CVE-based scenarios
const cveScenarios = [
  {
    cve: "CVE-2021-44228",
    title: "Log4Shell Vulnerability",
    category: "web-app-security",
    difficulty: "advanced",
    description: "Critical RCE vulnerability in Apache Log4j",
    realWorldImpact: "Affected millions of applications worldwide",
    tools: ["nmap", "burp-suite", "metasploit"],
    questions: [
      {
        type: "multiple_choice",
        text: "What makes the Log4Shell vulnerability (CVE-2021-44228) particularly dangerous?",
        options: [
          { text: "It affects only web applications", correct: false },
          { text: "It allows remote code execution through JNDI lookups", correct: true },
          { text: "It only works on Windows systems", correct: false },
          { text: "It requires physical access to exploit", correct: false }
        ],
        explanation: "Log4Shell allows attackers to execute arbitrary code by exploiting JNDI lookups in log messages, making it extremely dangerous due to its widespread use and ease of exploitation."
      },
      {
        type: "short_answer",
        text: "What JNDI protocol is commonly used to exploit Log4Shell?",
        correctAnswers: ["ldap", "LDAP", "ldap://"],
        explanation: "LDAP (Lightweight Directory Access Protocol) is the most commonly used JNDI protocol for Log4Shell exploitation."
      }
    ]
  },
  {
    cve: "CVE-2020-1472",
    title: "Zerologon Vulnerability",
    category: "network-security",
    difficulty: "advanced",
    description: "Critical privilege escalation in Windows Netlogon",
    realWorldImpact: "Allowed complete domain takeover",
    tools: ["impacket", "crackmapexec", "bloodhound"],
    questions: [
      {
        type: "multiple_choice",
        text: "What Windows service is affected by the Zerologon vulnerability?",
        options: [
          { text: "Active Directory Certificate Services", correct: false },
          { text: "Windows Remote Desktop Services", correct: false },
          { text: "Netlogon Remote Protocol", correct: true },
          { text: "Windows DNS Service", correct: false }
        ],
        explanation: "Zerologon affects the Netlogon Remote Protocol (MS-NRPC), which is used for authentication between domain controllers and clients."
      }
    ]
  }
];

// Bug bounty inspired scenarios
const bugBountyScenarios = [
  {
    title: "IDOR in User Profile Management",
    category: "web-app-security",
    difficulty: "intermediate",
    bountyAmount: "$2,500",
    platform: "HackerOne",
    description: "Insecure Direct Object Reference allowing access to other users' profiles",
    questions: [
      {
        type: "multiple_choice",
        text: "You discover that changing the 'user_id' parameter from 123 to 124 shows another user's profile. What vulnerability is this?",
        options: [
          { text: "SQL Injection", correct: false },
          { text: "Cross-Site Scripting", correct: false },
          { text: "Insecure Direct Object Reference", correct: true },
          { text: "Cross-Site Request Forgery", correct: false }
        ],
        explanation: "This is a classic IDOR vulnerability where the application doesn't properly verify that the user has permission to access the requested object."
      },
      {
        type: "short_answer",
        text: "What HTTP method would you typically use to test for IDOR vulnerabilities?",
        correctAnswers: ["GET", "POST", "PUT", "DELETE", "get", "post", "put", "delete"],
        explanation: "IDOR can be tested with any HTTP method, but GET and POST are most common. The key is manipulating object identifiers in requests."
      }
    ]
  }
];

// Tool-specific challenges
const toolChallenges = [
  {
    tool: "nmap",
    category: "network-security",
    difficulty: "intermediate",
    description: "Advanced Nmap scanning techniques",
    questions: [
      {
        type: "short_answer",
        text: "What Nmap flag performs a TCP SYN scan?",
        correctAnswers: ["-sS", "-sS ", "sS"],
        explanation: "The -sS flag performs a TCP SYN scan, also known as a 'half-open' scan."
      },
      {
        type: "multiple_choice",
        text: "Which Nmap script category would you use for vulnerability detection?",
        options: [
          { text: "safe", correct: false },
          { text: "intrusive", correct: false },
          { text: "vuln", correct: true },
          { text: "discovery", correct: false }
        ],
        explanation: "The 'vuln' script category contains scripts specifically designed for vulnerability detection."
      }
    ]
  },
  {
    tool: "burp-suite",
    category: "web-app-security",
    difficulty: "intermediate",
    description: "Web application testing with Burp Suite",
    questions: [
      {
        type: "multiple_choice",
        text: "Which Burp Suite tool is best for automated vulnerability scanning?",
        options: [
          { text: "Proxy", correct: false },
          { text: "Repeater", correct: false },
          { text: "Scanner", correct: true },
          { text: "Intruder", correct: false }
        ],
        explanation: "Burp Scanner is specifically designed for automated vulnerability detection in web applications."
      }
    ]
  }
];

// Compliance and framework questions
const complianceScenarios = [
  {
    framework: "NIST Cybersecurity Framework",
    category: "compliance-governance",
    difficulty: "intermediate",
    questions: [
      {
        type: "multiple_choice",
        text: "What are the five core functions of the NIST Cybersecurity Framework?",
        options: [
          { text: "Identify, Protect, Detect, Respond, Recover", correct: true },
          { text: "Plan, Implement, Check, Act, Improve", correct: false },
          { text: "Assess, Design, Implement, Monitor, Review", correct: false },
          { text: "Prevent, Detect, Contain, Eradicate, Recover", correct: false }
        ],
        explanation: "The NIST CSF consists of five core functions: Identify, Protect, Detect, Respond, and Recover."
      }
    ]
  }
];

interface QuestionTemplate {
  type: string;
  text: string;
  options?: Array<{ text: string; correct: boolean }>;
  correctAnswers?: string[];
  explanation: string;
  difficulty?: string;
  tools?: string[];
  cveReference?: string;
  realWorldScenario?: boolean;
}

interface ScenarioTemplate {
  title: string;
  category: string;
  difficulty: string;
  description: string;
  questions: QuestionTemplate[];
  cve?: string;
  tools?: string[];
  realWorldImpact?: string;
  bountyAmount?: string;
  platform?: string;
  framework?: string;
}

async function generateQuizContent() {
  console.log('🎯 Starting quiz content generation...');

  try {
    // Get admin user
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      throw new Error('Admin user not found. Run seed-enhanced-structure.ts first.');
    }

    // Get categories and difficulty levels
    const categories = await prisma.category.findMany();
    const difficulties = await prisma.difficultyLevel.findMany();

    console.log('📊 Generating CVE-based quizzes...');
    await generateScenarioQuizzes(cveScenarios, adminUser.id, categories, difficulties);

    console.log('🐛 Generating bug bounty scenario quizzes...');
    await generateScenarioQuizzes(bugBountyScenarios, adminUser.id, categories, difficulties);

    console.log('🔧 Generating tool-specific challenges...');
    await generateScenarioQuizzes(toolChallenges, adminUser.id, categories, difficulties);

    console.log('📋 Generating compliance scenario quizzes...');
    await generateScenarioQuizzes(complianceScenarios, adminUser.id, categories, difficulties);

    console.log('🎉 Quiz content generation completed successfully!');

  } catch (error) {
    console.error('❌ Error during quiz content generation:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function generateScenarioQuizzes(
  scenarios: any[],
  adminUserId: string,
  categories: any[],
  difficulties: any[]
) {
  for (const scenario of scenarios) {
    // Find matching category
    const category = categories.find(c => 
      c.slug === scenario.category || 
      c.name.toLowerCase().includes(scenario.category.replace('-', ' '))
    );

    // Find matching difficulty
    const difficulty = difficulties.find(d => 
      d.name.toLowerCase() === scenario.difficulty.toLowerCase()
    );

    if (!category || !difficulty) {
      console.warn(`⚠️  Skipping scenario ${scenario.title}: category or difficulty not found`);
      continue;
    }

    // Create quiz
    const quizId = `${scenario.category}-${scenario.title.toLowerCase().replace(/[^a-z0-9]/g, '-')}`;
    
    const quiz = await prisma.quiz.upsert({
      where: { quizId },
      update: {},
      create: {
        quizId,
        title: scenario.title,
        description: scenario.description,
        author: 'QuizFlow Security Team',
        tags: [
          scenario.category,
          scenario.difficulty,
          ...(scenario.tools || []),
          ...(scenario.cve ? [scenario.cve] : []),
          ...(scenario.framework ? [scenario.framework.toLowerCase().replace(/\s+/g, '-')] : [])
        ],
        passingScore: scenario.difficulty === 'advanced' ? 85 : scenario.difficulty === 'intermediate' ? 75 : 65,
        timeLimit: scenario.questions.length * 2, // 2 minutes per question
        categoryId: category.id,
        difficultyId: difficulty.id,
        isPublished: true,
        creatorId: adminUserId,
        sourceType: 'generated',
        realWorldScenario: Boolean(scenario.cve || scenario.bountyAmount),
        metadata: {
          cve: scenario.cve,
          bountyAmount: scenario.bountyAmount,
          platform: scenario.platform,
          realWorldImpact: scenario.realWorldImpact,
          framework: scenario.framework,
          tools: scenario.tools
        }
      }
    });

    // Create questions
    for (let i = 0; i < scenario.questions.length; i++) {
      const question = scenario.questions[i];
      const questionId = `${quizId}-q${i + 1}`;

      await prisma.question.create({
        data: {
          questionId,
          type: question.type,
          text: question.text,
          points: 1,
          explanation: question.explanation,
          options: question.options ? JSON.stringify(question.options) : null,
          correctAnswers: question.correctAnswers ? JSON.stringify(question.correctAnswers) : null,
          quizId: quiz.id,
          difficultyId: difficulty.id,
          topicTags: scenario.tools || [],
          realWorldScenario: Boolean(scenario.cve || scenario.bountyAmount),
          cveReference: scenario.cve,
          toolsRequired: scenario.tools || [],
          sourceReference: scenario.cve ? `https://cve.mitre.org/cgi-bin/cvename.cgi?name=${scenario.cve}` : undefined
        }
      });
    }

    console.log(`✅ Created quiz: ${scenario.title} with ${scenario.questions.length} questions`);
  }
}

// Run the generation function
if (require.main === module) {
  generateQuizContent();
}

export default generateQuizContent;
