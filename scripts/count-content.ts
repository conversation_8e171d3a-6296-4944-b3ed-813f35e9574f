#!/usr/bin/env tsx

/**
 * Content Counter Script
 *
 * This script counts the total quizzes and questions in the database
 * and provides a breakdown by category and difficulty.
 */

import { PrismaClient } from '@/generated/prisma';

const prisma = new PrismaClient();

async function countContent() {
  console.log('📊 Counting QuizFlow content...\n');

  try {
    // Get total counts
    const totalQuizzes = await prisma.quiz.count();
    const totalQuestions = await prisma.question.count();
    const totalUsers = await prisma.user.count();
    const totalResponses = await prisma.userResponse.count();

    console.log('🎯 **OVERALL STATISTICS**');
    console.log(`📚 Total Quizzes: ${totalQuizzes}`);
    console.log(`❓ Total Questions: ${totalQuestions}`);
    console.log(`👥 Total Users: ${totalUsers}`);
    console.log(`📝 Total Quiz Attempts: ${totalResponses}\n`);

    // Get quiz breakdown by category
    const quizzesByCategory = await prisma.quiz.findMany({
      include: {
        category: true,
        difficulty: true,
        _count: {
          select: {
            questions: true,
            responses: true
          }
        }
      },
      orderBy: {
        category: {
          name: 'asc'
        }
      }
    });

    // Group by category
    const categoryStats = quizzesByCategory.reduce((acc: any, quiz) => {
      const categoryName = quiz.category?.name || 'Uncategorized';
      if (!acc[categoryName]) {
        acc[categoryName] = {
          quizzes: 0,
          questions: 0,
          responses: 0,
          difficulties: new Set()
        };
      }
      acc[categoryName].quizzes += 1;
      acc[categoryName].questions += quiz._count.questions;
      acc[categoryName].responses += quiz._count.responses;
      if (quiz.difficulty) {
        acc[categoryName].difficulties.add(quiz.difficulty.name);
      }
      return acc;
    }, {});

    console.log('📁 **BREAKDOWN BY CATEGORY**');
    Object.entries(categoryStats).forEach(([category, stats]: [string, any]) => {
      console.log(`\n${category}:`);
      console.log(`  📚 Quizzes: ${stats.quizzes}`);
      console.log(`  ❓ Questions: ${stats.questions}`);
      console.log(`  📝 Attempts: ${stats.responses}`);
      console.log(`  🎯 Difficulties: ${Array.from(stats.difficulties).join(', ')}`);
    });

    // Get difficulty breakdown
    const difficultyCounts = await prisma.quiz.groupBy({
      by: ['difficultyId'],
      _count: {
        id: true
      }
    });

    const difficulties = await prisma.difficultyLevel.findMany();

    console.log('\n🎯 **BREAKDOWN BY DIFFICULTY**');
    for (const difficulty of difficulties) {
      const quizCount = difficultyCounts.find(d => d.difficultyId === difficulty.id)?._count.id || 0;
      const questionCount = await prisma.question.count({
        where: {
          difficultyId: difficulty.id
        }
      });

      console.log(`\n${difficulty.name} (Level ${difficulty.level}):`);
      console.log(`  📚 Quizzes: ${quizCount}`);
      console.log(`  ❓ Questions: ${questionCount}`);
    }

    // Get special content stats by checking tags and metadata
    const allQuizzes = await prisma.quiz.findMany({
      select: {
        tags: true,
        metadata: true
      }
    });

    const cveQuizzes = allQuizzes.filter(quiz =>
      quiz.tags.some(tag => tag.startsWith('CVE-')) ||
      (quiz.metadata && typeof quiz.metadata === 'object' && 'cve' in quiz.metadata)
    ).length;

    const realWorldQuizzes = allQuizzes.filter(quiz =>
      (quiz.metadata && typeof quiz.metadata === 'object' && 'realWorldScenario' in quiz.metadata)
    ).length;

    const toolBasedQuizzes = allQuizzes.filter(quiz =>
      quiz.tags.some(tag => ['nmap', 'burp-suite', 'metasploit', 'nuclei', 'impacket'].includes(tag)) ||
      (quiz.metadata && typeof quiz.metadata === 'object' && 'tools' in quiz.metadata)
    ).length;

    console.log('\n🔥 **SPECIAL CONTENT TYPES**');
    console.log(`🐛 CVE-Based Quizzes: ${cveQuizzes}`);
    console.log(`🌍 Real-World Scenarios: ${realWorldQuizzes}`);
    console.log(`🔧 Tool-Based Challenges: ${toolBasedQuizzes}`);

    // Get recent activity
    const recentQuizzes = await prisma.quiz.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        category: true,
        difficulty: true,
        _count: {
          select: {
            questions: true
          }
        }
      }
    });

    console.log('\n🆕 **RECENTLY ADDED QUIZZES**');
    recentQuizzes.forEach((quiz, index) => {
      console.log(`${index + 1}. ${quiz.title}`);
      console.log(`   📁 Category: ${quiz.category?.name || 'Uncategorized'}`);
      console.log(`   🎯 Difficulty: ${quiz.difficulty?.name || 'Unknown'}`);
      console.log(`   ❓ Questions: ${quiz._count.questions}`);
      console.log(`   📅 Created: ${quiz.createdAt.toLocaleDateString()}`);
    });

    // Calculate progress toward 1000 questions goal
    const progressPercentage = Math.round((totalQuestions / 1000) * 100);
    const remainingQuestions = Math.max(0, 1000 - totalQuestions);

    console.log('\n🎯 **PROGRESS TOWARD 1000 QUESTIONS GOAL**');
    console.log(`Current: ${totalQuestions}/1000 questions (${progressPercentage}%)`);
    console.log(`Remaining: ${remainingQuestions} questions needed`);

    // Progress bar
    const progressBar = '█'.repeat(Math.floor(progressPercentage / 5)) +
                       '░'.repeat(20 - Math.floor(progressPercentage / 5));
    console.log(`Progress: [${progressBar}] ${progressPercentage}%`);

    if (totalQuestions >= 1000) {
      console.log('🎉 **CONGRATULATIONS! You have reached the 1000 questions milestone!**');
    } else {
      console.log(`\n💡 **NEXT STEPS TO REACH 1000 QUESTIONS:**`);
      console.log(`- Add ${Math.ceil(remainingQuestions / 10)} more quizzes with ~10 questions each`);
      console.log(`- Focus on categories with fewer questions`);
      console.log(`- Add more CVE-based scenarios`);
      console.log(`- Create tool-specific challenges`);
      console.log(`- Develop compliance framework quizzes`);
    }

    console.log('\n✅ Content counting completed!');

  } catch (error) {
    console.error('❌ Error counting content:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the counting function
if (require.main === module) {
  countContent();
}

export default countContent;
