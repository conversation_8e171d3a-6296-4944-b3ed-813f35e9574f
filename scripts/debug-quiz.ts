#!/usr/bin/env tsx

/**
 * Debug Quiz Script
 * 
 * This script helps debug specific quiz issues by examining the raw data
 */

import { PrismaClient } from '@/generated/prisma';

const prisma = new PrismaClient();

async function debugQuiz(quizId?: string) {
  console.log('🔍 Debugging quiz data...\n');

  try {
    if (quizId) {
      // Debug specific quiz
      console.log(`🎯 Debugging quiz: ${quizId}\n`);
      
      const quiz = await prisma.quiz.findUnique({
        where: { id: quizId },
        include: {
          questions: true,
          category: true,
          difficulty: true
        }
      });

      if (!quiz) {
        console.log('❌ Quiz not found');
        return;
      }

      console.log('📚 Quiz Info:');
      console.log(`  Title: ${quiz.title}`);
      console.log(`  ID: ${quiz.id}`);
      console.log(`  Quiz ID: ${quiz.quizId}`);
      console.log(`  Category: ${quiz.category?.name || 'None'}`);
      console.log(`  Difficulty: ${quiz.difficulty?.name || 'None'}`);
      console.log(`  Questions: ${quiz.questions.length}\n`);

      console.log('❓ Questions Analysis:');
      quiz.questions.forEach((q, index) => {
        console.log(`\n${index + 1}. ${q.questionId} (${q.type})`);
        console.log(`   Text: ${typeof q.text === 'string' ? q.text.substring(0, 100) : JSON.stringify(q.text).substring(0, 100)}...`);
        
        // Check JSON fields
        const jsonFields = ['options', 'correctAnswer', 'correctAnswers', 'stems', 'correctPairs', 'textTemplate', 'blanks'];
        
        jsonFields.forEach(field => {
          const value = (q as any)[field];
          if (value) {
            console.log(`   ${field}: ${typeof value} - ${typeof value === 'string' ? value.substring(0, 50) : 'Object'}...`);
            
            // Try to parse if it's a string
            if (typeof value === 'string') {
              try {
                JSON.parse(value);
                console.log(`     ✅ Valid JSON`);
              } catch (error) {
                console.log(`     ❌ Invalid JSON: ${error.message}`);
              }
            }
          }
        });
      });

    } else {
      // List all quizzes
      const quizzes = await prisma.quiz.findMany({
        include: {
          _count: {
            select: {
              questions: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      console.log('📚 All Quizzes:');
      quizzes.forEach((quiz, index) => {
        console.log(`${index + 1}. ${quiz.title}`);
        console.log(`   ID: ${quiz.id}`);
        console.log(`   Quiz ID: ${quiz.quizId}`);
        console.log(`   Questions: ${quiz._count.questions}`);
        console.log(`   Published: ${quiz.isPublished}`);
        console.log('');
      });

      console.log('\n🔍 To debug a specific quiz, run:');
      console.log('npm run debug:quiz -- <quiz-id>');
    }

  } catch (error) {
    console.error('❌ Error debugging quiz:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Get quiz ID from command line arguments
const quizId = process.argv[2];

// Run the debug function
if (require.main === module) {
  debugQuiz(quizId);
}

export default debugQuiz;
