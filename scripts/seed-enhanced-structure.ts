#!/usr/bin/env tsx

/**
 * Enhanced Structure Seeding Script
 * 
 * This script sets up the enhanced database structure for scaling to 1000+ questions:
 * - Categories and subcategories
 * - Difficulty levels
 * - Learning paths
 * - Sample enhanced quiz structure
 */

import { PrismaClient } from '@/generated/prisma';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

// Category structure for cybersecurity domains
const categories = [
  {
    name: "Web Application Security",
    slug: "web-app-security",
    description: "Vulnerabilities and security testing of web applications",
    subcategories: [
      { name: "OWASP Top 10", slug: "owasp-top-10" },
      { name: "SQL Injection", slug: "sql-injection" },
      { name: "Cross-Site Scripting (XSS)", slug: "xss" },
      { name: "Authentication & Session Management", slug: "auth-session" },
      { name: "API Security", slug: "api-security" }
    ]
  },
  {
    name: "Cryptography & Encryption",
    slug: "cryptography",
    description: "Cryptographic algorithms, protocols, and implementations",
    subcategories: [
      { name: "Symmetric Encryption", slug: "symmetric-encryption" },
      { name: "Asymmetric Encryption", slug: "asymmetric-encryption" },
      { name: "Hash Functions", slug: "hash-functions" },
      { name: "Digital Signatures", slug: "digital-signatures" },
      { name: "PKI & Certificates", slug: "pki-certificates" }
    ]
  },
  {
    name: "Network Security",
    slug: "network-security",
    description: "Network protocols, scanning, and penetration testing",
    subcategories: [
      { name: "Network Scanning", slug: "network-scanning" },
      { name: "Protocol Analysis", slug: "protocol-analysis" },
      { name: "Wireless Security", slug: "wireless-security" },
      { name: "Firewall & IDS/IPS", slug: "firewall-ids-ips" },
      { name: "VPN & Tunneling", slug: "vpn-tunneling" }
    ]
  },
  {
    name: "Mobile Security",
    slug: "mobile-security",
    description: "Mobile application and device security",
    subcategories: [
      { name: "Android Security", slug: "android-security" },
      { name: "iOS Security", slug: "ios-security" },
      { name: "Mobile App Testing", slug: "mobile-app-testing" },
      { name: "Mobile Device Management", slug: "mobile-device-management" }
    ]
  },
  {
    name: "Cloud Security",
    slug: "cloud-security",
    description: "Cloud infrastructure and service security",
    subcategories: [
      { name: "AWS Security", slug: "aws-security" },
      { name: "Azure Security", slug: "azure-security" },
      { name: "GCP Security", slug: "gcp-security" },
      { name: "Container Security", slug: "container-security" },
      { name: "Serverless Security", slug: "serverless-security" }
    ]
  },
  {
    name: "Social Engineering",
    slug: "social-engineering",
    description: "Human psychology and social manipulation techniques",
    subcategories: [
      { name: "Phishing", slug: "phishing" },
      { name: "Pretexting", slug: "pretexting" },
      { name: "Physical Security", slug: "physical-security" },
      { name: "OSINT", slug: "osint" }
    ]
  },
  {
    name: "Malware Analysis",
    slug: "malware-analysis",
    description: "Analysis and reverse engineering of malicious software",
    subcategories: [
      { name: "Static Analysis", slug: "static-analysis" },
      { name: "Dynamic Analysis", slug: "dynamic-analysis" },
      { name: "Reverse Engineering", slug: "reverse-engineering" },
      { name: "Sandbox Analysis", slug: "sandbox-analysis" }
    ]
  },
  {
    name: "Incident Response",
    slug: "incident-response",
    description: "Security incident handling and forensics",
    subcategories: [
      { name: "Digital Forensics", slug: "digital-forensics" },
      { name: "Incident Handling", slug: "incident-handling" },
      { name: "Threat Hunting", slug: "threat-hunting" },
      { name: "Recovery & Lessons Learned", slug: "recovery-lessons" }
    ]
  },
  {
    name: "Compliance & Governance",
    slug: "compliance-governance",
    description: "Security frameworks, standards, and compliance",
    subcategories: [
      { name: "ISO 27001", slug: "iso-27001" },
      { name: "NIST Framework", slug: "nist-framework" },
      { name: "PCI DSS", slug: "pci-dss" },
      { name: "GDPR", slug: "gdpr" },
      { name: "SOX", slug: "sox" }
    ]
  },
  {
    name: "Emerging Threats",
    slug: "emerging-threats",
    description: "Latest threats and attack techniques",
    subcategories: [
      { name: "AI/ML Security", slug: "ai-ml-security" },
      { name: "IoT Security", slug: "iot-security" },
      { name: "Blockchain Security", slug: "blockchain-security" },
      { name: "Supply Chain Attacks", slug: "supply-chain-attacks" }
    ]
  }
];

// Difficulty levels
const difficultyLevels = [
  {
    name: "Beginner",
    level: 1,
    description: "Basic concepts and fundamental knowledge",
    color: "#22c55e" // green
  },
  {
    name: "Intermediate",
    level: 2,
    description: "Practical application and real-world scenarios",
    color: "#f59e0b" // amber
  },
  {
    name: "Advanced",
    level: 3,
    description: "Complex multi-step attacks and expert-level knowledge",
    color: "#ef4444" // red
  }
];

// Learning paths
const learningPaths = [
  {
    name: "Web Application Security Fundamentals",
    slug: "web-app-security-fundamentals",
    description: "Complete introduction to web application security testing",
    difficulty: "beginner",
    estimatedHours: 20,
    categorySlug: "web-app-security"
  },
  {
    name: "Advanced Penetration Testing",
    slug: "advanced-penetration-testing",
    description: "Advanced techniques for professional penetration testers",
    difficulty: "advanced",
    estimatedHours: 40,
    categorySlug: "network-security"
  },
  {
    name: "Cryptography for Security Professionals",
    slug: "cryptography-for-security-professionals",
    description: "Comprehensive cryptography knowledge for cybersecurity",
    difficulty: "intermediate",
    estimatedHours: 30,
    categorySlug: "cryptography"
  },
  {
    name: "Incident Response & Digital Forensics",
    slug: "incident-response-digital-forensics",
    description: "Complete incident response and forensics workflow",
    difficulty: "intermediate",
    estimatedHours: 35,
    categorySlug: "incident-response"
  },
  {
    name: "Cloud Security Mastery",
    slug: "cloud-security-mastery",
    description: "Comprehensive cloud security across all major platforms",
    difficulty: "advanced",
    estimatedHours: 45,
    categorySlug: "cloud-security"
  }
];

async function seedEnhancedStructure() {
  console.log('🌱 Starting enhanced structure seeding...');

  try {
    // Ensure admin user exists
    let adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      console.log('👤 Creating admin user...');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'QuizFlow Admin',
          role: 'admin',
          password: hashedPassword,
          experienceLevel: 'expert',
          specializations: ['penetration-testing', 'web-security', 'network-security']
        }
      });
      console.log('✅ Admin user created');
    }

    // Create difficulty levels
    console.log('📊 Creating difficulty levels...');
    for (const difficulty of difficultyLevels) {
      await prisma.difficultyLevel.upsert({
        where: { name: difficulty.name },
        update: {},
        create: difficulty
      });
    }
    console.log('✅ Difficulty levels created');

    // Create categories and subcategories
    console.log('📁 Creating categories...');
    for (const category of categories) {
      const createdCategory = await prisma.category.upsert({
        where: { slug: category.slug },
        update: {},
        create: {
          name: category.name,
          slug: category.slug,
          description: category.description
        }
      });

      // Create subcategories
      for (const subcategory of category.subcategories) {
        await prisma.category.upsert({
          where: { slug: subcategory.slug },
          update: {},
          create: {
            name: subcategory.name,
            slug: subcategory.slug,
            parentId: createdCategory.id
          }
        });
      }
    }
    console.log('✅ Categories and subcategories created');

    // Create learning paths
    console.log('🛤️  Creating learning paths...');
    for (const path of learningPaths) {
      const category = await prisma.category.findUnique({
        where: { slug: path.categorySlug }
      });

      if (category) {
        await prisma.learningPath.upsert({
          where: { slug: path.slug },
          update: {},
          create: {
            name: path.name,
            slug: path.slug,
            description: path.description,
            difficulty: path.difficulty,
            estimatedHours: path.estimatedHours,
            categoryId: category.id,
            creatorId: adminUser.id,
            isPublished: true,
            quizOrder: [] // Will be populated when quizzes are created
          }
        });
      }
    }
    console.log('✅ Learning paths created');

    console.log('🎉 Enhanced structure seeding completed successfully!');

  } catch (error) {
    console.error('❌ Error during enhanced structure seeding:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
if (require.main === module) {
  seedEnhancedStructure();
}

export default seedEnhancedStructure;
