
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  parentId: 'parentId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DifficultyLevelScalarFieldEnum = {
  id: 'id',
  name: 'name',
  level: 'level',
  description: 'description',
  color: 'color',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuizScalarFieldEnum = {
  id: 'id',
  quizId: 'quizId',
  title: 'title',
  description: 'description',
  author: 'author',
  creationDate: 'creationDate',
  tags: 'tags',
  passingScore: 'passingScore',
  timeLimit: 'timeLimit',
  markupFormat: 'markupFormat',
  locale: 'locale',
  formatVersion: 'formatVersion',
  isPublished: 'isPublished',
  categoryId: 'categoryId',
  difficultyId: 'difficultyId',
  estimatedTime: 'estimatedTime',
  prerequisites: 'prerequisites',
  learningPath: 'learningPath',
  version: 'version',
  isTemplate: 'isTemplate',
  sourceType: 'sourceType',
  metadata: 'metadata',
  creatorId: 'creatorId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuestionScalarFieldEnum = {
  id: 'id',
  questionId: 'questionId',
  type: 'type',
  text: 'text',
  points: 'points',
  feedbackCorrect: 'feedbackCorrect',
  feedbackIncorrect: 'feedbackIncorrect',
  explanation: 'explanation',
  media: 'media',
  hint: 'hint',
  dependsOn: 'dependsOn',
  options: 'options',
  correctAnswer: 'correctAnswer',
  correctAnswers: 'correctAnswers',
  caseSensitive: 'caseSensitive',
  trimWhitespace: 'trimWhitespace',
  exactMatch: 'exactMatch',
  stems: 'stems',
  correctPairs: 'correctPairs',
  textTemplate: 'textTemplate',
  blanks: 'blanks',
  minWordCount: 'minWordCount',
  maxWordCount: 'maxWordCount',
  guidelines: 'guidelines',
  difficultyId: 'difficultyId',
  topicTags: 'topicTags',
  skillLevel: 'skillLevel',
  estimatedTime: 'estimatedTime',
  realWorldScenario: 'realWorldScenario',
  cveReference: 'cveReference',
  toolsRequired: 'toolsRequired',
  sourceReference: 'sourceReference',
  lastValidated: 'lastValidated',
  validatedBy: 'validatedBy',
  quizId: 'quizId',
  questionPoolId: 'questionPoolId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.QuestionPoolScalarFieldEnum = {
  id: 'id',
  poolId: 'poolId',
  title: 'title',
  description: 'description',
  quizId: 'quizId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SelectionRuleScalarFieldEnum = {
  id: 'id',
  poolId: 'poolId',
  selectCount: 'selectCount',
  randomize: 'randomize',
  shuffleOrder: 'shuffleOrder',
  quizId: 'quizId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  emailVerified: 'emailVerified',
  password: 'password',
  image: 'image',
  role: 'role',
  bio: 'bio',
  location: 'location',
  website: 'website',
  linkedinUrl: 'linkedinUrl',
  githubUrl: 'githubUrl',
  twitterUrl: 'twitterUrl',
  specializations: 'specializations',
  experienceLevel: 'experienceLevel',
  totalPoints: 'totalPoints',
  level: 'level',
  badges: 'badges',
  streak: 'streak',
  lastActiveDate: 'lastActiveDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires'
};

exports.Prisma.LearningPathScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  slug: 'slug',
  categoryId: 'categoryId',
  difficulty: 'difficulty',
  estimatedHours: 'estimatedHours',
  isPublished: 'isPublished',
  quizOrder: 'quizOrder',
  creatorId: 'creatorId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LearningPathEnrollmentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  learningPathId: 'learningPathId',
  enrolledAt: 'enrolledAt',
  completedAt: 'completedAt',
  currentQuizIndex: 'currentQuizIndex',
  progress: 'progress',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserResponseScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  quizId: 'quizId',
  answers: 'answers',
  score: 'score',
  startedAt: 'startedAt',
  completedAt: 'completedAt',
  timeSpent: 'timeSpent',
  attemptNumber: 'attemptNumber',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  hintsUsed: 'hintsUsed',
  questionsCorrect: 'questionsCorrect',
  questionsTotal: 'questionsTotal',
  questionAnalytics: 'questionAnalytics',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserSkillScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  skillName: 'skillName',
  level: 'level',
  experience: 'experience',
  lastPracticed: 'lastPracticed',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};


exports.Prisma.ModelName = {
  Category: 'Category',
  DifficultyLevel: 'DifficultyLevel',
  Quiz: 'Quiz',
  Question: 'Question',
  QuestionPool: 'QuestionPool',
  SelectionRule: 'SelectionRule',
  User: 'User',
  Account: 'Account',
  Session: 'Session',
  LearningPath: 'LearningPath',
  LearningPathEnrollment: 'LearningPathEnrollment',
  UserResponse: 'UserResponse',
  UserSkill: 'UserSkill'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
