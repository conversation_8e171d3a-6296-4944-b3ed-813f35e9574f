"use client";

import React, { useState, useEffect } from "react";
import { Question } from "@/types/qfjson";
import { getLocalizedText } from "@/lib/utils/qfjson-parser";
import ReactMarkdown from "react-markdown";
// import { cn } from "@/lib/utils";

// Import question type components
import MultipleChoiceQuestion from "./question-types/MultipleChoiceQuestion";
import TrueFalseQuestion from "./question-types/TrueFalseQuestion";
import ShortAnswerQuestion from "./question-types/ShortAnswerQuestion";
import MatchingQuestion from "./question-types/MatchingQuestion";
import FillInTheBlankQuestion from "./question-types/FillInTheBlankQuestion";
import EssayQuestion from "./question-types/EssayQuestion";

interface QuestionRendererProps {
  question: Question;
  answer: any;
  onAnswerChange: (answer: any) => void;
  locale?: string;
  showFeedback?: boolean;
  showExplanation?: boolean;
  showAnswerButton?: boolean;
  onShowAnswer?: () => void;
}

const QuestionRenderer: React.FC<QuestionRendererProps> = ({
  question,
  answer,
  onAnswerChange,
  locale = "en-US",
  showFeedback = false,
  showExplanation = false,
  showAnswerButton = false,
  onShowAnswer,
}) => {
  const [visibleHints, setVisibleHints] = useState<string[]>([]);
  const [availableHints, setAvailableHints] = useState<string[]>([]);
  const [hintTimers, setHintTimers] = useState<Map<string, NodeJS.Timeout>>(new Map());
  const [showAnswer, setShowAnswer] = useState(false);

  // Handle hint visibility based on delay_seconds
  useEffect(() => {
    if (!question.hint || question.hint.length === 0) return;

    const timers = new Map<string, NodeJS.Timeout>();
    const immediate: string[] = [];
    const delayed: string[] = [];

    question.hint.forEach((hint) => {
      if (hint.delay_seconds && hint.delay_seconds > 0) {
        // Add to delayed hints that will become available later
        const timer = setTimeout(() => {
          setAvailableHints(prev => [...prev, hint.text]);
        }, hint.delay_seconds * 1000);

        timers.set(hint.text, timer);
        delayed.push(hint.text);
      } else {
        // Show hints with no delay immediately
        immediate.push(hint.text);
      }
    });

    setVisibleHints(immediate);
    setHintTimers(timers);

    return () => {
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [question.hint]);

  // Show a hint manually
  const showHint = (hintText: string) => {
    setVisibleHints(prev => [...prev, hintText]);
    setAvailableHints(prev => prev.filter(h => h !== hintText));
  };

  // Get the correct answer for display
  const getCorrectAnswer = () => {
    switch (question.type) {
      case "multiple_choice": {
        const mcQuestion = question as any;
        const correctOptions = mcQuestion.options?.filter((opt: any) => opt.is_correct) || [];
        return correctOptions.map((opt: any) => getLocalizedText(opt.text, locale)).join(", ");
      }
      case "true_false": {
        const tfQuestion = question as any;
        return tfQuestion.correct_answer ? "True" : "False";
      }
      case "short_answer": {
        const saQuestion = question as any;
        return saQuestion.correct_answers?.[0] || "No answer provided";
      }
      case "matching": {
        const matchQuestion = question as any;
        if (!matchQuestion.correct_pairs || !matchQuestion.stems || !matchQuestion.options) {
          return "Matching pairs not available";
        }
        return matchQuestion.correct_pairs.map((pair: any) => {
          const stem = matchQuestion.stems.find((s: any) => s.id === pair.stem_id);
          const option = matchQuestion.options.find((o: any) => o.id === pair.option_id);
          return `${getLocalizedText(stem?.text || "", locale)} → ${getLocalizedText(option?.text || "", locale)}`;
        }).join("; ");
      }
      case "fill_in_the_blank": {
        const fibQuestion = question as any;
        if (!fibQuestion.blanks) return "No blanks defined";
        return fibQuestion.blanks.map((blank: any, index: number) =>
          `Blank ${index + 1}: ${blank.correct_answers?.[0] || "No answer"}`
        ).join("; ");
      }
      case "essay": {
        return "Essay questions don't have a single correct answer";
      }
      default:
        return "Answer format not supported";
    }
  };

  // Handle show answer button click
  const handleShowAnswer = () => {
    setShowAnswer(true);
    if (onShowAnswer) {
      onShowAnswer();
    }
  };

  // Render question text with markdown support
  const renderQuestionText = () => {
    const text = getLocalizedText(question.text, locale);
    return (
      <div className="prose dark:prose-invert max-w-none">
        <ReactMarkdown>
          {text}
        </ReactMarkdown>
      </div>
    );
  };

  // Render media items
  const renderMedia = () => {
    if (!question.media || question.media.length === 0) return null;

    return (
      <div className="mt-4 space-y-4">
        {question.media.map((media, index) => {
          if (media.type === "image") {
            return (
              <figure key={index} className="relative">
                <img
                  src={media.url}
                  alt={media.alt_text || "Question image"}
                  className="max-w-full rounded-md"
                />
                {media.caption && (
                  <figcaption className="text-sm text-muted-foreground mt-2">
                    {media.caption}
                  </figcaption>
                )}
              </figure>
            );
          } else if (media.type === "video") {
            return (
              <figure key={index} className="relative">
                <video
                  src={media.url}
                  controls
                  className="max-w-full rounded-md"
                />
                {media.caption && (
                  <figcaption className="text-sm text-muted-foreground mt-2">
                    {media.caption}
                  </figcaption>
                )}
              </figure>
            );
          } else if (media.type === "audio") {
            return (
              <figure key={index} className="relative">
                <audio src={media.url} controls className="w-full" />
                {media.caption && (
                  <figcaption className="text-sm text-muted-foreground mt-2">
                    {media.caption}
                  </figcaption>
                )}
              </figure>
            );
          }
          return null;
        })}
      </div>
    );
  };

  // Render hints
  const renderHints = () => {
    const hasVisibleHints = visibleHints.length > 0;
    const hasAvailableHints = availableHints.length > 0;

    if (!hasVisibleHints && !hasAvailableHints) return null;

    return (
      <div className="mt-4 space-y-3">
        {hasVisibleHints && (
          <div className="p-4 bg-muted rounded-md">
            <h4 className="font-medium mb-2">💡 Hints:</h4>
            <ul className="list-disc pl-5 space-y-1">
              {visibleHints.map((hint, index) => (
                <li key={index} className="text-sm">{hint}</li>
              ))}
            </ul>
          </div>
        )}

        {hasAvailableHints && (
          <div className="flex flex-wrap gap-2">
            {availableHints.map((hint, index) => (
              <button
                key={index}
                onClick={() => showHint(hint)}
                className="px-3 py-1 text-sm bg-yellow-100 hover:bg-yellow-200 text-yellow-800 rounded-md transition-colors border border-yellow-300"
              >
                💡 Show Hint {visibleHints.length + index + 1}
              </button>
            ))}
          </div>
        )}
      </div>
    );
  };

  // Render feedback if showFeedback is true
  const renderFeedback = () => {
    if (!showFeedback) return null;

    // This is a simplified implementation - in a real app, you would need
    // to determine if the answer is correct based on question type
    const isCorrect = false; // Replace with actual logic

    if (isCorrect && question.feedback_correct) {
      return (
        <div className="mt-4 p-4 bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-50 rounded-md">
          <ReactMarkdown>{question.feedback_correct}</ReactMarkdown>
        </div>
      );
    } else if (!isCorrect && question.feedback_incorrect) {
      return (
        <div className="mt-4 p-4 bg-red-50 text-red-800 dark:bg-red-900 dark:text-red-50 rounded-md">
          <ReactMarkdown>{question.feedback_incorrect}</ReactMarkdown>
        </div>
      );
    }

    return null;
  };

  // Render explanation if showExplanation is true
  const renderExplanation = () => {
    if (!showExplanation || !question.explanation) return null;

    return (
      <div className="mt-4 p-4 bg-blue-50 text-blue-800 dark:bg-blue-900 dark:text-blue-50 rounded-md">
        <h4 className="font-medium mb-2">Explanation:</h4>
        <ReactMarkdown>{getLocalizedText(question.explanation, locale)}</ReactMarkdown>
      </div>
    );
  };

  // Render show answer section
  const renderShowAnswer = () => {
    if (!showAnswerButton && !showAnswer) return null;

    return (
      <div className="mt-4">
        {!showAnswer && showAnswerButton && (
          <button
            onClick={handleShowAnswer}
            className="px-4 py-2 bg-orange-100 hover:bg-orange-200 text-orange-800 rounded-md transition-colors border border-orange-300 font-medium"
          >
            🔍 Show Answer
          </button>
        )}

        {showAnswer && (
          <div className="p-4 bg-green-50 text-green-800 dark:bg-green-900 dark:text-green-50 rounded-md">
            <h4 className="font-medium mb-2">✅ Correct Answer:</h4>
            <div className="text-sm font-mono bg-white dark:bg-gray-800 p-2 rounded border">
              {getCorrectAnswer()}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render the appropriate question type component
  const renderQuestionTypeComponent = () => {
    switch (question.type) {
      case "multiple_choice":
        return (
          <MultipleChoiceQuestion
            question={question as any}
            answer={answer}
            onAnswerChange={onAnswerChange}
            locale={locale}
          />
        );
      case "true_false":
        return (
          <TrueFalseQuestion
            question={question as any}
            answer={answer}
            onAnswerChange={onAnswerChange}
          />
        );
      case "short_answer":
        return (
          <ShortAnswerQuestion
            question={question as any}
            answer={answer}
            onAnswerChange={onAnswerChange}
          />
        );
      case "matching":
        return (
          <MatchingQuestion
            question={question as any}
            answer={answer}
            onAnswerChange={onAnswerChange}
            locale={locale}
          />
        );
      case "fill_in_the_blank":
        return (
          <FillInTheBlankQuestion
            question={question as any}
            answer={answer}
            onAnswerChange={onAnswerChange}
            locale={locale}
          />
        );
      case "essay":
        return (
          <EssayQuestion
            question={question as any}
            answer={answer}
            onAnswerChange={onAnswerChange}
          />
        );
      default:
        return (
          <div className="p-4 bg-yellow-50 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-50 rounded-md">
            Unsupported question type: {(question as any).type}
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        {renderQuestionText()}
        {renderMedia()}
      </div>

      <div className="mt-4">
        {renderQuestionTypeComponent()}
      </div>

      {renderHints()}
      {renderShowAnswer()}
      {renderFeedback()}
      {renderExplanation()}
    </div>
  );
};

export default QuestionRenderer;
