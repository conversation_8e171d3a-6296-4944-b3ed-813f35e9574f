# Hints and Explanations in QuizFlow

QuizFlow supports advanced hint and explanation features that enhance the learning experience by providing progressive assistance and detailed explanations.

## Features Overview

### 1. **Progressive Hints with Delayed Showing**
- Hints can be configured to appear immediately or after a specified delay
- Multiple hints per question with different delay timers
- Interactive "Show Hint" buttons for delayed hints
- Visual indicators and progressive numbering

### 2. **Comprehensive Explanations**
- Question-level explanations shown after completion
- Option-level explanations for multiple choice questions
- Support for multilingual content
- Markdown formatting support

### 3. **Configurable Show Answer Button**
- Optional "Show Answer" button to reveal correct answers
- Supports all question types with appropriate formatting
- Configurable per quiz or question instance
- Callback support for tracking usage

## Implementation Details

### Hint Configuration

Hints are defined in the QFJSON format using the `hint` array:

```json
{
  "hint": [
    {
      "text": "This hint appears immediately",
      "delay_seconds": 0
    },
    {
      "text": "This hint appears after 30 seconds",
      "delay_seconds": 30
    },
    {
      "text": "This hint appears after 60 seconds",
      "delay_seconds": 60
    }
  ]
}
```

### Explanation Configuration

Explanations can be added at multiple levels:

#### Question-Level Explanations
```json
{
  "question_id": "q1",
  "type": "multiple_choice",
  "text": "What is the primary purpose of a firewall?",
  "explanation": "Firewalls are fundamental network security devices that examine incoming and outgoing network traffic and decide whether to allow or block specific traffic based on a defined set of security rules.",
  "options": [...]
}
```

#### Option-Level Explanations
```json
{
  "options": [
    {
      "id": "opt1",
      "text": "To encrypt all network traffic",
      "is_correct": false,
      "explanation": "Firewalls don't encrypt traffic - that's the job of protocols like TLS/SSL or VPN technologies."
    },
    {
      "id": "opt2",
      "text": "To control and monitor network traffic",
      "is_correct": true,
      "explanation": "Correct! Firewalls act as a barrier between trusted and untrusted networks, filtering traffic based on predetermined security rules."
    }
  ]
}
```

### Show Answer Button Configuration

The Show Answer button is configured through component props:

```typescript
<QuestionRenderer
  question={question}
  answer={answer}
  onAnswerChange={onAnswerChange}
  showAnswerButton={true}
  onShowAnswer={() => console.log('Answer revealed')}
/>
```

#### Answer Display Formats

**Multiple Choice:**
```
Option A, Option B (for multiple correct answers)
```

**True/False:**
```
True / False
```

**Short Answer:**
```
First correct answer from the array
```

**Matching:**
```
Stem 1 → Option A; Stem 2 → Option B
```

**Fill-in-the-Blank:**
```
Blank 1: answer1; Blank 2: answer2
```

**Essay:**
```
Essay questions don't have a single correct answer
```

## User Experience

### Hint Display Behavior

1. **Immediate Hints**: Shown as soon as the question loads
2. **Delayed Hints**:
   - Timer starts when question is displayed
   - "Show Hint" button appears when timer expires
   - User can click to reveal the hint
   - Progressive numbering (Hint 1, Hint 2, etc.)

### Show Answer Button Behavior

1. **Button Display**: Orange-themed button with magnifying glass emoji (🔍)
2. **Answer Revelation**: Click to reveal formatted correct answer
3. **Answer Display**: Green background with checkmark emoji (✅)
4. **Callback Trigger**: Optional callback fired when answer is shown

### Visual Design

- **Hints**: Light gray background with lightbulb emoji (💡)
- **Show Answer Button**: Orange background with magnifying glass emoji (🔍)
- **Revealed Answer**: Green background with checkmark emoji (✅)
- **Explanations**: Blue background for educational content
- **Feedback**: Green for correct, red for incorrect responses
- **Interactive Elements**: Hover effects and smooth transitions

### Accessibility Features

- Clear visual hierarchy
- Descriptive button labels
- Keyboard navigation support
- Screen reader friendly content

## Technical Implementation

### Component Structure

```typescript
interface QuestionRendererProps {
  question: Question;
  answer: any;
  onAnswerChange: (answer: any) => void;
  locale?: string;
  showFeedback?: boolean;
  showExplanation?: boolean;
  showAnswerButton?: boolean; // New prop for show answer button
  onShowAnswer?: () => void;   // Callback when answer is revealed
}
```

### State Management

```typescript
const [visibleHints, setVisibleHints] = useState<string[]>([]);
const [availableHints, setAvailableHints] = useState<string[]>([]);
const [hintTimers, setHintTimers] = useState<Map<string, NodeJS.Timeout>>(new Map());
const [showAnswer, setShowAnswer] = useState(false); // Track answer visibility
```

### Timer Logic

- Automatic timers for delayed hints
- Cleanup on component unmount
- Manual hint revelation through user interaction

## Database Schema

The `Question` model includes:

```prisma
model Question {
  // ... other fields
  explanation       Json?     // General explanation (string or multilingual object)
  hint              Json?     // Array of hint objects with delay_seconds
  // ... other fields
}
```

## Best Practices

### Hint Design
1. **Progressive Difficulty**: Start with general hints, become more specific
2. **Educational Value**: Hints should teach concepts, not just give answers
3. **Appropriate Timing**: Use delays to encourage thinking before assistance

### Explanation Content
1. **Comprehensive Coverage**: Explain both correct and incorrect options
2. **Educational Context**: Provide broader context and learning objectives
3. **Clear Language**: Use accessible language appropriate for the audience

### Performance Considerations
1. **Timer Cleanup**: Always clear timers on component unmount
2. **Memory Management**: Efficient state updates for hint visibility
3. **Responsive Design**: Ensure hints work well on all device sizes

## Example Usage

### Demo Page
Visit `/demo/show-answer` for an interactive demonstration of all features:
- Progressive hints with delayed showing
- Configurable Show Answer button
- Answer display for different question types
- Explanations and feedback integration

### Sample Data
See `src/data/enhanced-hints-quiz.json` for a complete example demonstrating:
- Multiple hints with different delays
- Question and option-level explanations
- Various question types with enhanced features
- Multilingual support capabilities

### Integration Example
```typescript
<QuizRenderer
  quiz={quizData}
  showAnswerButton={true}
  onComplete={(score, answers) => {
    console.log('Quiz completed:', score, answers);
  }}
/>
```

This implementation provides a rich, interactive learning experience that guides users through complex concepts while maintaining engagement and educational value. The Show Answer button adds an additional layer of support for learners who need immediate assistance or want to verify their understanding.
