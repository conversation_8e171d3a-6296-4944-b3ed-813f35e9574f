// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

// Category model for organizing quizzes
model Category {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String    @unique
  description String?
  slug        String    @unique
  parentId    String?   @db.ObjectId
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  children    Category[] @relation("CategoryHierarchy")
  quizzes     Quiz[]

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// Difficulty level model
model DifficultyLevel {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String    @unique // "Beginner", "Intermediate", "Advanced"
  level       Int       @unique // 1, 2, 3
  description String?
  color       String?   // Hex color for UI
  quizzes     Quiz[]
  questions   Question[]

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// Quiz model
model Quiz {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  quizId          String    @unique // The quiz_id from the QFJSON format
  title           String
  description     String?
  author          String?
  creationDate    DateTime  @default(now())
  tags            String[]
  passingScore    Float?
  timeLimit       Int?      // in minutes
  markupFormat    String    @default("markdown")
  locale          String    @default("en-US")
  formatVersion   String    @default("1.1")
  isPublished     Boolean   @default(false)

  // New fields for scalability
  categoryId      String?   @db.ObjectId
  category        Category? @relation(fields: [categoryId], references: [id])
  difficultyId    String?   @db.ObjectId
  difficulty      DifficultyLevel? @relation(fields: [difficultyId], references: [id])
  estimatedTime   Int?      // estimated completion time in minutes
  prerequisites   String[]  // Array of quiz IDs that should be completed first
  learningPath    String?   // Learning path this quiz belongs to
  version         String    @default("1.0")
  isTemplate      Boolean   @default(false) // For quiz templates
  sourceType      String    @default("manual") // "manual", "imported", "generated"
  metadata        Json?     // Additional metadata for complex scenarios

  // Relations
  questions       Question[]
  questionPools   QuestionPool[]
  selectionRules  SelectionRule[]
  responses       UserResponse[]
  creator         User?     @relation("CreatedBy", fields: [creatorId], references: [id])
  creatorId       String?   @db.ObjectId

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

// Question model
model Question {
  id                String    @id @default(auto()) @map("_id") @db.ObjectId
  questionId        String    // The question_id from the QFJSON format
  type              String    // multiple_choice, true_false, short_answer, etc.
  text              Json      // Can be a string or an object for multilingual support
  points            Float
  feedbackCorrect   String?
  feedbackIncorrect String?
  explanation       Json?     // General explanation shown after answering (can be string or multilingual object)
  media             Json?     // Array of media objects
  hint              Json?     // Array of hint objects
  dependsOn         Json?     // Dependency object

  // Type-specific fields stored as JSON
  options           Json?     // For multiple_choice
  correctAnswer     Json?     // For true_false, short_answer
  correctAnswers    Json?     // For short_answer
  caseSensitive     Boolean?  // For short_answer
  trimWhitespace    Boolean?  // For short_answer
  exactMatch        Boolean?  // For short_answer
  stems             Json?     // For matching
  correctPairs      Json?     // For matching
  textTemplate      Json?     // For fill_in_the_blank
  blanks            Json?     // For fill_in_the_blank
  minWordCount      Int?      // For essay
  maxWordCount      Int?      // For essay
  guidelines        String?   // For essay

  // New fields for enhanced question management
  difficultyId      String?   @db.ObjectId
  difficulty        DifficultyLevel? @relation(fields: [difficultyId], references: [id])
  topicTags         String[]  // Specific topic tags for granular filtering
  skillLevel        String?   // "basic", "intermediate", "advanced", "expert"
  estimatedTime     Int?      // estimated time to answer in seconds
  realWorldScenario Boolean   @default(false) // Is this based on real incidents/CVEs?
  cveReference      String?   // CVE number if applicable
  toolsRequired     String[]  // Tools needed to solve this question
  sourceReference   String?   // Reference to source material, research paper, etc.
  lastValidated     DateTime? // When this question was last validated for accuracy
  validatedBy       String?   // Who validated this question

  // Relations
  quiz              Quiz      @relation(fields: [quizId], references: [id], onDelete: Cascade)
  quizId            String    @db.ObjectId
  questionPool      QuestionPool? @relation(fields: [questionPoolId], references: [id])
  questionPoolId    String?   @db.ObjectId

  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}

// QuestionPool model
model QuestionPool {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  poolId      String    // The pool_id from the QFJSON format
  title       String?
  description String?
  questions   Question[]

  // Relations
  quiz        Quiz      @relation(fields: [quizId], references: [id], onDelete: Cascade)
  quizId      String    @db.ObjectId

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// SelectionRule model
model SelectionRule {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  poolId      String    // References the pool_id in QuestionPool
  selectCount Int
  randomize   Boolean   @default(false)
  shuffleOrder Boolean  @default(false)

  // Relations
  quiz        Quiz      @relation(fields: [quizId], references: [id], onDelete: Cascade)
  quizId      String    @db.ObjectId

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// User model for authentication
model User {
  id              String    @id @default(auto()) @map("_id") @db.ObjectId
  name            String?
  email           String    @unique
  emailVerified   DateTime?
  password        String?
  image           String?
  role            String    @default("user") // "user", "admin", "instructor"

  // Enhanced user profile
  bio             String?
  location        String?
  website         String?
  linkedinUrl     String?
  githubUrl       String?
  twitterUrl      String?
  specializations String[] // Areas of cybersecurity expertise
  experienceLevel String?  // "beginner", "intermediate", "advanced", "expert"

  // Gamification
  totalPoints     Int       @default(0)
  level           Int       @default(1)
  badges          String[]  // Array of earned badge IDs
  streak          Int       @default(0) // Current daily streak
  lastActiveDate  DateTime?

  // Relations
  createdQuizzes  Quiz[]    @relation("CreatedBy")
  createdLearningPaths LearningPath[] @relation("CreatedLearningPaths")
  responses       UserResponse[]
  pathEnrollments LearningPathEnrollment[]
  skills          UserSkill[]
  accounts        Account[]
  sessions        Session[]

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

// Account model for OAuth providers
model Account {
  id                String  @id @default(auto()) @map("_id") @db.ObjectId
  userId            String  @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.String
  access_token      String? @db.String
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.String
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

// Session model for managing user sessions
model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// Learning Path model for structured learning
model LearningPath {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  slug        String    @unique
  categoryId  String?   @db.ObjectId
  category    Category? @relation(fields: [categoryId], references: [id])
  difficulty  String    // "beginner", "intermediate", "advanced"
  estimatedHours Int?    // Total estimated completion time
  isPublished Boolean   @default(false)

  // Ordered list of quiz IDs in the learning path
  quizOrder   String[]  // Array of quiz IDs in order

  // Relations
  enrollments LearningPathEnrollment[]
  creator     User?     @relation("CreatedLearningPaths", fields: [creatorId], references: [id])
  creatorId   String?   @db.ObjectId

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// User enrollment in learning paths
model LearningPathEnrollment {
  id             String       @id @default(auto()) @map("_id") @db.ObjectId
  user           User         @relation(fields: [userId], references: [id])
  userId         String       @db.ObjectId
  learningPath   LearningPath @relation(fields: [learningPathId], references: [id])
  learningPathId String       @db.ObjectId

  enrolledAt     DateTime     @default(now())
  completedAt    DateTime?
  currentQuizIndex Int        @default(0) // Current position in the learning path
  progress       Float        @default(0) // Percentage completion

  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@unique([userId, learningPathId])
}

// Enhanced UserResponse model to track quiz attempts
model UserResponse {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  user        User?     @relation(fields: [userId], references: [id])
  userId      String?   @db.ObjectId
  quiz        Quiz      @relation(fields: [quizId], references: [id])
  quizId      String    @db.ObjectId
  answers     Json      // JSON object with question IDs as keys and user answers as values
  score       Float
  startedAt   DateTime  @default(now())
  completedAt DateTime?
  timeSpent   Int?      // in seconds

  // Enhanced tracking
  attemptNumber Int      @default(1) // Which attempt this is for this user/quiz
  ipAddress     String?  // For security/analytics
  userAgent     String?  // Browser/device info
  hintsUsed     Int      @default(0) // Number of hints used
  questionsCorrect Int   @default(0) // Number of questions answered correctly
  questionsTotal   Int   @default(0) // Total number of questions in this attempt

  // Detailed question-level analytics
  questionAnalytics Json? // Detailed per-question timing and attempt data

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// User skill tracking
model UserSkill {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  user       User     @relation(fields: [userId], references: [id])
  userId     String   @db.ObjectId
  skillName  String   // e.g., "SQL Injection", "XSS", "Network Scanning"
  level      Float    @default(0) // 0-100 skill level
  experience Int      @default(0) // Total experience points in this skill
  lastPracticed DateTime?

  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@unique([userId, skillName])
}
