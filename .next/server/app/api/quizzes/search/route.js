const CHUNK_PUBLIC_PATH = "server/app/api/quizzes/search/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/[root of the server]__f9c338._.js");
runtime.loadChunk("server/chunks/node_modules_next_997bc5._.js");
runtime.loadChunk("server/chunks/node_modules_next-auth_d209f9._.js");
runtime.loadChunk("server/chunks/node_modules_openid-client_ef38b3._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_cjs_b4a801._.js");
runtime.loadChunk("server/chunks/node_modules_a242f1._.js");
runtime.loadChunk("server/chunks/_a16ee1._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/quizzes/search/route/actions.js [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/quizzes/search/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
