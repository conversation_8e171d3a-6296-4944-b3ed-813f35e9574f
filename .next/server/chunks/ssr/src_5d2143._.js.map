{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,sMAAM,UAAU,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/analytics/AnalyticsOverview.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\n\ninterface AnalyticsOverviewProps {\n  totalQuizzes: number;\n  totalQuizzesTaken: number;\n  totalResponsesReceived: number;\n  averageScore: number;\n  completionRate: number;\n}\n\nexport default function AnalyticsOverview({\n  totalQuizzes,\n  totalQuizzesTaken,\n  totalResponsesReceived,\n  averageScore,\n  completionRate,\n}: AnalyticsOverviewProps) {\n  return (\n    <>\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col space-y-2\">\n            <p className=\"text-sm text-muted-foreground\">Total Quizzes Created</p>\n            <p className=\"text-3xl font-bold\">{totalQuizzes}</p>\n          </div>\n        </CardContent>\n      </Card>\n      \n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col space-y-2\">\n            <p className=\"text-sm text-muted-foreground\">Quizzes Taken</p>\n            <p className=\"text-3xl font-bold\">{totalQuizzesTaken}</p>\n          </div>\n        </CardContent>\n      </Card>\n      \n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col space-y-2\">\n            <p className=\"text-sm text-muted-foreground\">Responses Received</p>\n            <p className=\"text-3xl font-bold\">{totalResponsesReceived}</p>\n          </div>\n        </CardContent>\n      </Card>\n      \n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex flex-col space-y-2\">\n            <p className=\"text-sm text-muted-foreground\">Average Score</p>\n            <p className=\"text-3xl font-bold\">{averageScore.toFixed(1)}%</p>\n          </div>\n        </CardContent>\n      </Card>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYe,SAAS,kBAAkB,EACxC,YAAY,EACZ,iBAAiB,EACjB,sBAAsB,EACtB,YAAY,EACZ,cAAc,EACS;IACvB,qBACE;;0BACE,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;0CAC7C,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;;;;;;;;;;;0BAKzC,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;0CAC7C,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;;;;;;;;;;;0BAKzC,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;0CAC7C,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;;;;;;;;;;;0BAKzC,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;0CAC7C,8OAAC;gCAAE,WAAU;;oCAAsB,aAAa,OAAO,CAAC;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;AAMvE"}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/analytics/QuizPerformanceChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef } from \"react\";\n\ninterface QuizPerformanceData {\n  quizTitle: string;\n  responseCount: number;\n  averageScore: number;\n}\n\ninterface QuizPerformanceChartProps {\n  data: QuizPerformanceData[];\n}\n\nexport default function QuizPerformanceChart({ data }: QuizPerformanceChartProps) {\n  const chartRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (!chartRef.current || data.length === 0) return;\n\n    // This is a placeholder for a chart library\n    // In a real implementation, you would use a library like Chart.js, Recharts, or D3.js\n    \n    const chartContainer = chartRef.current;\n    chartContainer.innerHTML = \"\";\n    \n    // Create a simple bar chart representation\n    const chartHeight = 300;\n    const barWidth = Math.floor((chartContainer.clientWidth - 100) / data.length);\n    const maxResponseCount = Math.max(...data.map(d => d.responseCount));\n    \n    const chart = document.createElement(\"div\");\n    chart.style.position = \"relative\";\n    chart.style.height = `${chartHeight}px`;\n    chart.style.display = \"flex\";\n    chart.style.alignItems = \"flex-end\";\n    chart.style.justifyContent = \"space-around\";\n    chart.style.padding = \"0 20px\";\n    \n    // Add y-axis labels\n    const yAxis = document.createElement(\"div\");\n    yAxis.style.position = \"absolute\";\n    yAxis.style.left = \"0\";\n    yAxis.style.top = \"0\";\n    yAxis.style.height = \"100%\";\n    yAxis.style.display = \"flex\";\n    yAxis.style.flexDirection = \"column\";\n    yAxis.style.justifyContent = \"space-between\";\n    \n    const yLabels = [100, 75, 50, 25, 0];\n    yLabels.forEach(label => {\n      const yLabel = document.createElement(\"div\");\n      yLabel.style.fontSize = \"12px\";\n      yLabel.style.color = \"#666\";\n      yLabel.textContent = `${label}%`;\n      yAxis.appendChild(yLabel);\n    });\n    \n    chart.appendChild(yAxis);\n    \n    // Add bars\n    data.forEach((item, index) => {\n      const barContainer = document.createElement(\"div\");\n      barContainer.style.display = \"flex\";\n      barContainer.style.flexDirection = \"column\";\n      barContainer.style.alignItems = \"center\";\n      barContainer.style.width = `${barWidth}px`;\n      \n      // Score bar\n      const scoreBar = document.createElement(\"div\");\n      const scoreHeight = (item.averageScore / 100) * chartHeight;\n      scoreBar.style.height = `${scoreHeight}px`;\n      scoreBar.style.width = `${barWidth * 0.4}px`;\n      scoreBar.style.backgroundColor = \"#3b82f6\";\n      scoreBar.style.borderRadius = \"4px 4px 0 0\";\n      \n      // Response count bar\n      const responseBar = document.createElement(\"div\");\n      const responseHeight = (item.responseCount / maxResponseCount) * chartHeight;\n      responseBar.style.height = `${responseHeight}px`;\n      responseBar.style.width = `${barWidth * 0.4}px`;\n      responseBar.style.backgroundColor = \"#10b981\";\n      responseBar.style.borderRadius = \"4px 4px 0 0\";\n      responseBar.style.marginLeft = \"4px\";\n      \n      const barGroup = document.createElement(\"div\");\n      barGroup.style.display = \"flex\";\n      barGroup.style.alignItems = \"flex-end\";\n      barGroup.style.height = \"100%\";\n      barGroup.appendChild(scoreBar);\n      barGroup.appendChild(responseBar);\n      \n      barContainer.appendChild(barGroup);\n      \n      // Label\n      const label = document.createElement(\"div\");\n      label.style.fontSize = \"12px\";\n      label.style.marginTop = \"8px\";\n      label.style.textAlign = \"center\";\n      label.style.whiteSpace = \"nowrap\";\n      label.style.overflow = \"hidden\";\n      label.style.textOverflow = \"ellipsis\";\n      label.style.width = \"100%\";\n      label.textContent = item.quizTitle;\n      barContainer.appendChild(label);\n      \n      chart.appendChild(barContainer);\n    });\n    \n    // Add legend\n    const legend = document.createElement(\"div\");\n    legend.style.display = \"flex\";\n    legend.style.justifyContent = \"center\";\n    legend.style.marginTop = \"20px\";\n    \n    const scoreLegend = document.createElement(\"div\");\n    scoreLegend.style.display = \"flex\";\n    scoreLegend.style.alignItems = \"center\";\n    scoreLegend.style.marginRight = \"20px\";\n    \n    const scoreColor = document.createElement(\"div\");\n    scoreColor.style.width = \"12px\";\n    scoreColor.style.height = \"12px\";\n    scoreColor.style.backgroundColor = \"#3b82f6\";\n    scoreColor.style.marginRight = \"4px\";\n    \n    const scoreText = document.createElement(\"span\");\n    scoreText.textContent = \"Average Score\";\n    \n    scoreLegend.appendChild(scoreColor);\n    scoreLegend.appendChild(scoreText);\n    \n    const responseLegend = document.createElement(\"div\");\n    responseLegend.style.display = \"flex\";\n    responseLegend.style.alignItems = \"center\";\n    \n    const responseColor = document.createElement(\"div\");\n    responseColor.style.width = \"12px\";\n    responseColor.style.height = \"12px\";\n    responseColor.style.backgroundColor = \"#10b981\";\n    responseColor.style.marginRight = \"4px\";\n    \n    const responseText = document.createElement(\"span\");\n    responseText.textContent = \"Response Count\";\n    \n    responseLegend.appendChild(responseColor);\n    responseLegend.appendChild(responseText);\n    \n    legend.appendChild(scoreLegend);\n    legend.appendChild(responseLegend);\n    \n    chartContainer.appendChild(chart);\n    chartContainer.appendChild(legend);\n    \n  }, [data]);\n\n  if (data.length === 0) {\n    return (\n      <div className=\"flex items-center justify-center h-[300px] bg-muted/20 rounded-md\">\n        <p className=\"text-muted-foreground\">No data available</p>\n      </div>\n    );\n  }\n\n  return (\n    <div ref={chartRef} className=\"h-[400px]\">\n      {/* Chart will be rendered here */}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAce,SAAS,qBAAqB,EAAE,IAAI,EAA6B;IAC9E,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;QAE5C,4CAA4C;QAC5C,sFAAsF;QAEtF,MAAM,iBAAiB,SAAS,OAAO;QACvC,eAAe,SAAS,GAAG;QAE3B,2CAA2C;QAC3C,MAAM,cAAc;QACpB,MAAM,WAAW,KAAK,KAAK,CAAC,CAAC,eAAe,WAAW,GAAG,GAAG,IAAI,KAAK,MAAM;QAC5E,MAAM,mBAAmB,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,aAAa;QAElE,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,KAAK,CAAC,QAAQ,GAAG;QACvB,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,EAAE,CAAC;QACvC,MAAM,KAAK,CAAC,OAAO,GAAG;QACtB,MAAM,KAAK,CAAC,UAAU,GAAG;QACzB,MAAM,KAAK,CAAC,cAAc,GAAG;QAC7B,MAAM,KAAK,CAAC,OAAO,GAAG;QAEtB,oBAAoB;QACpB,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,KAAK,CAAC,QAAQ,GAAG;QACvB,MAAM,KAAK,CAAC,IAAI,GAAG;QACnB,MAAM,KAAK,CAAC,GAAG,GAAG;QAClB,MAAM,KAAK,CAAC,MAAM,GAAG;QACrB,MAAM,KAAK,CAAC,OAAO,GAAG;QACtB,MAAM,KAAK,CAAC,aAAa,GAAG;QAC5B,MAAM,KAAK,CAAC,cAAc,GAAG;QAE7B,MAAM,UAAU;YAAC;YAAK;YAAI;YAAI;YAAI;SAAE;QACpC,QAAQ,OAAO,CAAC,CAAA;YACd,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,OAAO,KAAK,CAAC,QAAQ,GAAG;YACxB,OAAO,KAAK,CAAC,KAAK,GAAG;YACrB,OAAO,WAAW,GAAG,GAAG,MAAM,CAAC,CAAC;YAChC,MAAM,WAAW,CAAC;QACpB;QAEA,MAAM,WAAW,CAAC;QAElB,WAAW;QACX,KAAK,OAAO,CAAC,CAAC,MAAM;YAClB,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,KAAK,CAAC,OAAO,GAAG;YAC7B,aAAa,KAAK,CAAC,aAAa,GAAG;YACnC,aAAa,KAAK,CAAC,UAAU,GAAG;YAChC,aAAa,KAAK,CAAC,KAAK,GAAG,GAAG,SAAS,EAAE,CAAC;YAE1C,YAAY;YACZ,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,MAAM,cAAc,AAAC,KAAK,YAAY,GAAG,MAAO;YAChD,SAAS,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,EAAE,CAAC;YAC1C,SAAS,KAAK,CAAC,KAAK,GAAG,GAAG,WAAW,IAAI,EAAE,CAAC;YAC5C,SAAS,KAAK,CAAC,eAAe,GAAG;YACjC,SAAS,KAAK,CAAC,YAAY,GAAG;YAE9B,qBAAqB;YACrB,MAAM,cAAc,SAAS,aAAa,CAAC;YAC3C,MAAM,iBAAiB,AAAC,KAAK,aAAa,GAAG,mBAAoB;YACjE,YAAY,KAAK,CAAC,MAAM,GAAG,GAAG,eAAe,EAAE,CAAC;YAChD,YAAY,KAAK,CAAC,KAAK,GAAG,GAAG,WAAW,IAAI,EAAE,CAAC;YAC/C,YAAY,KAAK,CAAC,eAAe,GAAG;YACpC,YAAY,KAAK,CAAC,YAAY,GAAG;YACjC,YAAY,KAAK,CAAC,UAAU,GAAG;YAE/B,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,KAAK,CAAC,OAAO,GAAG;YACzB,SAAS,KAAK,CAAC,UAAU,GAAG;YAC5B,SAAS,KAAK,CAAC,MAAM,GAAG;YACxB,SAAS,WAAW,CAAC;YACrB,SAAS,WAAW,CAAC;YAErB,aAAa,WAAW,CAAC;YAEzB,QAAQ;YACR,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,KAAK,CAAC,QAAQ,GAAG;YACvB,MAAM,KAAK,CAAC,SAAS,GAAG;YACxB,MAAM,KAAK,CAAC,SAAS,GAAG;YACxB,MAAM,KAAK,CAAC,UAAU,GAAG;YACzB,MAAM,KAAK,CAAC,QAAQ,GAAG;YACvB,MAAM,KAAK,CAAC,YAAY,GAAG;YAC3B,MAAM,KAAK,CAAC,KAAK,GAAG;YACpB,MAAM,WAAW,GAAG,KAAK,SAAS;YAClC,aAAa,WAAW,CAAC;YAEzB,MAAM,WAAW,CAAC;QACpB;QAEA,aAAa;QACb,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,KAAK,CAAC,OAAO,GAAG;QACvB,OAAO,KAAK,CAAC,cAAc,GAAG;QAC9B,OAAO,KAAK,CAAC,SAAS,GAAG;QAEzB,MAAM,cAAc,SAAS,aAAa,CAAC;QAC3C,YAAY,KAAK,CAAC,OAAO,GAAG;QAC5B,YAAY,KAAK,CAAC,UAAU,GAAG;QAC/B,YAAY,KAAK,CAAC,WAAW,GAAG;QAEhC,MAAM,aAAa,SAAS,aAAa,CAAC;QAC1C,WAAW,KAAK,CAAC,KAAK,GAAG;QACzB,WAAW,KAAK,CAAC,MAAM,GAAG;QAC1B,WAAW,KAAK,CAAC,eAAe,GAAG;QACnC,WAAW,KAAK,CAAC,WAAW,GAAG;QAE/B,MAAM,YAAY,SAAS,aAAa,CAAC;QACzC,UAAU,WAAW,GAAG;QAExB,YAAY,WAAW,CAAC;QACxB,YAAY,WAAW,CAAC;QAExB,MAAM,iBAAiB,SAAS,aAAa,CAAC;QAC9C,eAAe,KAAK,CAAC,OAAO,GAAG;QAC/B,eAAe,KAAK,CAAC,UAAU,GAAG;QAElC,MAAM,gBAAgB,SAAS,aAAa,CAAC;QAC7C,cAAc,KAAK,CAAC,KAAK,GAAG;QAC5B,cAAc,KAAK,CAAC,MAAM,GAAG;QAC7B,cAAc,KAAK,CAAC,eAAe,GAAG;QACtC,cAAc,KAAK,CAAC,WAAW,GAAG;QAElC,MAAM,eAAe,SAAS,aAAa,CAAC;QAC5C,aAAa,WAAW,GAAG;QAE3B,eAAe,WAAW,CAAC;QAC3B,eAAe,WAAW,CAAC;QAE3B,OAAO,WAAW,CAAC;QACnB,OAAO,WAAW,CAAC;QAEnB,eAAe,WAAW,CAAC;QAC3B,eAAe,WAAW,CAAC;IAE7B,GAAG;QAAC;KAAK;IAET,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;IAG3C;IAEA,qBACE,8OAAC;QAAI,KAAK;QAAU,WAAU;;;;;;AAIlC"}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/analytics/TopQuizzesChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef } from \"react\";\n\ninterface TopQuizData {\n  title: string;\n  responseCount: number;\n}\n\ninterface TopQuizzesChartProps {\n  data: TopQuizData[];\n}\n\nexport default function TopQuizzesChart({ data }: TopQuizzesChartProps) {\n  const chartRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (!chartRef.current || data.length === 0) return;\n\n    // This is a placeholder for a chart library\n    // In a real implementation, you would use a library like Chart.js, Recharts, or D3.js\n    \n    const chartContainer = chartRef.current;\n    chartContainer.innerHTML = \"\";\n    \n    // Create a simple horizontal bar chart\n    const chartHeight = 300;\n    const barHeight = Math.floor(chartHeight / (data.length * 2));\n    const maxResponseCount = Math.max(...data.map(d => d.responseCount));\n    \n    const chart = document.createElement(\"div\");\n    chart.style.position = \"relative\";\n    chart.style.height = `${chartHeight}px`;\n    chart.style.display = \"flex\";\n    chart.style.flexDirection = \"column\";\n    chart.style.justifyContent = \"space-around\";\n    chart.style.padding = \"10px 0\";\n    \n    // Add bars\n    data.forEach((item, index) => {\n      const barContainer = document.createElement(\"div\");\n      barContainer.style.display = \"flex\";\n      barContainer.style.alignItems = \"center\";\n      barContainer.style.height = `${barHeight * 2}px`;\n      \n      // Label\n      const label = document.createElement(\"div\");\n      label.style.width = \"40%\";\n      label.style.fontSize = \"14px\";\n      label.style.whiteSpace = \"nowrap\";\n      label.style.overflow = \"hidden\";\n      label.style.textOverflow = \"ellipsis\";\n      label.style.paddingRight = \"10px\";\n      label.textContent = item.title;\n      \n      // Bar\n      const barWrapper = document.createElement(\"div\");\n      barWrapper.style.width = \"60%\";\n      barWrapper.style.height = `${barHeight}px`;\n      barWrapper.style.backgroundColor = \"#e5e7eb\";\n      barWrapper.style.borderRadius = \"4px\";\n      barWrapper.style.overflow = \"hidden\";\n      \n      const bar = document.createElement(\"div\");\n      const barWidth = (item.responseCount / maxResponseCount) * 100;\n      bar.style.width = `${barWidth}%`;\n      bar.style.height = \"100%\";\n      bar.style.backgroundColor = \"#3b82f6\";\n      \n      // Count\n      const count = document.createElement(\"div\");\n      count.style.marginLeft = \"10px\";\n      count.style.fontSize = \"14px\";\n      count.style.fontWeight = \"bold\";\n      count.textContent = item.responseCount.toString();\n      \n      barWrapper.appendChild(bar);\n      barContainer.appendChild(label);\n      barContainer.appendChild(barWrapper);\n      barContainer.appendChild(count);\n      \n      chart.appendChild(barContainer);\n    });\n    \n    chartContainer.appendChild(chart);\n    \n  }, [data]);\n\n  if (data.length === 0) {\n    return (\n      <div className=\"flex items-center justify-center h-[300px] bg-muted/20 rounded-md\">\n        <p className=\"text-muted-foreground\">No data available</p>\n      </div>\n    );\n  }\n\n  return (\n    <div ref={chartRef} className=\"h-[300px]\">\n      {/* Chart will be rendered here */}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAae,SAAS,gBAAgB,EAAE,IAAI,EAAwB;IACpE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;QAE5C,4CAA4C;QAC5C,sFAAsF;QAEtF,MAAM,iBAAiB,SAAS,OAAO;QACvC,eAAe,SAAS,GAAG;QAE3B,uCAAuC;QACvC,MAAM,cAAc;QACpB,MAAM,YAAY,KAAK,KAAK,CAAC,cAAc,CAAC,KAAK,MAAM,GAAG,CAAC;QAC3D,MAAM,mBAAmB,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,aAAa;QAElE,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,KAAK,CAAC,QAAQ,GAAG;QACvB,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,EAAE,CAAC;QACvC,MAAM,KAAK,CAAC,OAAO,GAAG;QACtB,MAAM,KAAK,CAAC,aAAa,GAAG;QAC5B,MAAM,KAAK,CAAC,cAAc,GAAG;QAC7B,MAAM,KAAK,CAAC,OAAO,GAAG;QAEtB,WAAW;QACX,KAAK,OAAO,CAAC,CAAC,MAAM;YAClB,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,KAAK,CAAC,OAAO,GAAG;YAC7B,aAAa,KAAK,CAAC,UAAU,GAAG;YAChC,aAAa,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,EAAE,EAAE,CAAC;YAEhD,QAAQ;YACR,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,KAAK,CAAC,KAAK,GAAG;YACpB,MAAM,KAAK,CAAC,QAAQ,GAAG;YACvB,MAAM,KAAK,CAAC,UAAU,GAAG;YACzB,MAAM,KAAK,CAAC,QAAQ,GAAG;YACvB,MAAM,KAAK,CAAC,YAAY,GAAG;YAC3B,MAAM,KAAK,CAAC,YAAY,GAAG;YAC3B,MAAM,WAAW,GAAG,KAAK,KAAK;YAE9B,MAAM;YACN,MAAM,aAAa,SAAS,aAAa,CAAC;YAC1C,WAAW,KAAK,CAAC,KAAK,GAAG;YACzB,WAAW,KAAK,CAAC,MAAM,GAAG,GAAG,UAAU,EAAE,CAAC;YAC1C,WAAW,KAAK,CAAC,eAAe,GAAG;YACnC,WAAW,KAAK,CAAC,YAAY,GAAG;YAChC,WAAW,KAAK,CAAC,QAAQ,GAAG;YAE5B,MAAM,MAAM,SAAS,aAAa,CAAC;YACnC,MAAM,WAAW,AAAC,KAAK,aAAa,GAAG,mBAAoB;YAC3D,IAAI,KAAK,CAAC,KAAK,GAAG,GAAG,SAAS,CAAC,CAAC;YAChC,IAAI,KAAK,CAAC,MAAM,GAAG;YACnB,IAAI,KAAK,CAAC,eAAe,GAAG;YAE5B,QAAQ;YACR,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,KAAK,CAAC,UAAU,GAAG;YACzB,MAAM,KAAK,CAAC,QAAQ,GAAG;YACvB,MAAM,KAAK,CAAC,UAAU,GAAG;YACzB,MAAM,WAAW,GAAG,KAAK,aAAa,CAAC,QAAQ;YAE/C,WAAW,WAAW,CAAC;YACvB,aAAa,WAAW,CAAC;YACzB,aAAa,WAAW,CAAC;YACzB,aAAa,WAAW,CAAC;YAEzB,MAAM,WAAW,CAAC;QACpB;QAEA,eAAe,WAAW,CAAC;IAE7B,GAAG;QAAC;KAAK;IAET,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;IAG3C;IAEA,qBACE,8OAAC;QAAI,KAAK;QAAU,WAAU;;;;;;AAIlC"}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hacking-quiz/src/components/analytics/RecentActivityTable.tsx"], "sourcesContent": ["\"use client\";\n\nimport { UserResponse, Quiz, User } from \"@/generated/prisma\";\nimport { formatDate } from \"@/lib/utils\";\n\ninterface ResponseWithRelations extends UserResponse {\n  quiz: Quiz;\n  user: {\n    id: string;\n    name: string | null;\n    email: string | null;\n  } | null;\n}\n\ninterface RecentActivityTableProps {\n  responses: ResponseWithRelations[];\n}\n\nexport default function RecentActivityTable({ responses }: RecentActivityTableProps) {\n  if (responses.length === 0) {\n    return (\n      <div className=\"text-center py-8\">\n        <p className=\"text-muted-foreground\">No recent activity</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"overflow-x-auto\">\n      <table className=\"w-full\">\n        <thead>\n          <tr className=\"border-b\">\n            <th className=\"text-left py-3 px-4 font-medium\">Quiz</th>\n            <th className=\"text-left py-3 px-4 font-medium\">User</th>\n            <th className=\"text-left py-3 px-4 font-medium\">Score</th>\n            <th className=\"text-left py-3 px-4 font-medium\">Time Spent</th>\n            <th className=\"text-left py-3 px-4 font-medium\">Date</th>\n            <th className=\"text-left py-3 px-4 font-medium\">Status</th>\n          </tr>\n        </thead>\n        <tbody>\n          {responses.map((response) => (\n            <tr key={response.id} className=\"border-b hover:bg-muted/50\">\n              <td className=\"py-3 px-4\">{response.quiz.title}</td>\n              <td className=\"py-3 px-4\">\n                {response.user?.name || response.user?.email || \"Anonymous\"}\n              </td>\n              <td className=\"py-3 px-4\">{response.score.toFixed(1)}%</td>\n              <td className=\"py-3 px-4\">\n                {response.timeSpent\n                  ? formatTimeSpent(response.timeSpent)\n                  : \"N/A\"}\n              </td>\n              <td className=\"py-3 px-4\">\n                {formatDate(response.completedAt || response.startedAt)}\n              </td>\n              <td className=\"py-3 px-4\">\n                <span\n                  className={`inline-block px-2 py-1 text-xs rounded-full ${\n                    response.completedAt\n                      ? \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100\"\n                      : \"bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100\"\n                  }`}\n                >\n                  {response.completedAt ? \"Completed\" : \"In Progress\"}\n                </span>\n              </td>\n            </tr>\n          ))}\n        </tbody>\n      </table>\n    </div>\n  );\n}\n\nfunction formatTimeSpent(seconds: number): string {\n  if (seconds < 60) {\n    return `${seconds} sec`;\n  }\n\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n\n  if (minutes < 60) {\n    return `${minutes} min ${remainingSeconds} sec`;\n  }\n\n  const hours = Math.floor(minutes / 60);\n  const remainingMinutes = minutes % 60;\n\n  return `${hours} hr ${remainingMinutes} min`;\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAkBe,SAAS,oBAAoB,EAAE,SAAS,EAA4B;IACjF,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;IAG3C;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAM,WAAU;;8BACf,8OAAC;8BACC,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;;;;;;;;;;;;8BAGpD,8OAAC;8BACE,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;4BAAqB,WAAU;;8CAC9B,8OAAC;oCAAG,WAAU;8CAAa,SAAS,IAAI,CAAC,KAAK;;;;;;8CAC9C,8OAAC;oCAAG,WAAU;8CACX,SAAS,IAAI,EAAE,QAAQ,SAAS,IAAI,EAAE,SAAS;;;;;;8CAElD,8OAAC;oCAAG,WAAU;;wCAAa,SAAS,KAAK,CAAC,OAAO,CAAC;wCAAG;;;;;;;8CACrD,8OAAC;oCAAG,WAAU;8CACX,SAAS,SAAS,GACf,gBAAgB,SAAS,SAAS,IAClC;;;;;;8CAEN,8OAAC;oCAAG,WAAU;8CACX,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD,EAAE,SAAS,WAAW,IAAI,SAAS,SAAS;;;;;;8CAExD,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCACC,WAAW,CAAC,4CAA4C,EACtD,SAAS,WAAW,GAChB,sEACA,qEACJ;kDAED,SAAS,WAAW,GAAG,cAAc;;;;;;;;;;;;2BAtBnC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;AA+BhC;AAEA,SAAS,gBAAgB,OAAe;IACtC,IAAI,UAAU,IAAI;QAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;IACzB;IAEA,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,UAAU;IAEnC,IAAI,UAAU,IAAI;QAChB,OAAO,GAAG,QAAQ,KAAK,EAAE,iBAAiB,IAAI,CAAC;IACjD;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,mBAAmB,UAAU;IAEnC,OAAO,GAAG,MAAM,IAAI,EAAE,iBAAiB,IAAI,CAAC;AAC9C"}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}