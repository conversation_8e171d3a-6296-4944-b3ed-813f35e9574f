{"name": "hacking-quiz", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "seed": "tsx scripts/seed-quizzes.ts", "setup-admin": "tsx scripts/setup-admin.ts", "check-admin": "tsx scripts/check-admin.ts", "test-login": "tsx scripts/test-login.ts", "db:push": "prisma db push", "db:reset": "prisma db push --force-reset", "seed:enhanced": "tsx scripts/seed-enhanced-structure.ts", "generate:content": "tsx scripts/generate-quiz-content.ts", "count:content": "tsx scripts/count-content.ts", "debug:quiz": "tsx scripts/debug-quiz.ts", "setup:full": "npm run db:push && npm run seed:enhanced && npm run seed && npm run generate:content", "setup:production": "npm run db:push && npm run seed:enhanced && npm run generate:content"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@prisma/client": "^6.8.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "marked": "^15.0.12", "mongodb": "^6.16.0", "mongoose": "^8.15.0", "next": "15.1.8", "next-auth": "^4.24.11", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "shadcn-ui": "^0.9.5", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.27"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcrypt": "^5.0.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.2", "typescript": "^5"}}